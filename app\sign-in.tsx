import Button from "@/features/shared/components/Button";
import GoogleSignInButton from "@/features/shared/components/GoogleSignInButton";
import InputField from "@/features/shared/components/InputField";
import StyledOtpInput from "@/features/shared/components/StyledOtpInput";
import AuthHeader from "@/features/auth/components/AuthHeader";
import InfoLink from "@/features/auth/components/InfoLink";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import { createHandleErrorDialog } from "@/lib/errors";
import globalStyles from "@/lib/globalStyles";
import { useSignIn } from "@clerk/clerk-expo";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { TFunction } from "i18next";
import React, { useCallback, useState, useTransition } from "react";
import { useTranslation } from "react-i18next";
import * as WebBrowser from "expo-web-browser";
import { ScrollView, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { z } from "zod";
import Loading from "@/features/shared/components/Loading";

const signInSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    email: z.string().email(t("auth.invalid_email")),
  });

const verificationSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    code: z.string().length(6, t("auth.code_length", { length: 6 })),
  });

WebBrowser.maybeCompleteAuthSession();

export default function SignInScreen() {
  const { signIn, setActive, isLoaded } = useSignIn();
  const { handleGoogleOAuth, useWarmUpBrowser } = useAppAuth();
  const [pendingVerification, setPendingVerification] = useState(false);
  const [code, setCode] = useState("");

  const { t } = useTranslation();

  // Warm up browser for better UX
  useWarmUpBrowser();

  const [formFields, setFormFields] = useState({
    email: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isGooglePending, startGoogleTransition] = useTransition();

  const handleChange = (fieldName: keyof typeof formFields, value: string) => {
    setFormFields({
      ...formFields,
      [fieldName]: value,
    });
  };

  const handleEmailSignIn = useCallback(async () => {
    try {
      if (!isLoaded) return;
      setIsLoading(true);

      const validatedData = signInSchema(t).parse(formFields);

      await signIn.create({
        identifier: validatedData.email,
        strategy: "email_code",
      });

      setPendingVerification(true);
    } catch (err: any) {
      createHandleErrorDialog({
        title: t("auth.error_signing_in"),
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, formFields.email]);

  const handleVerifyCode = async (code: string) => {
    try {
      if (!isLoaded) return;
      setIsLoading(true);
      const validatedCode = verificationSchema(t).parse({ code });

      const signInAttempt = await signIn.attemptFirstFactor({
        code: validatedCode.code,
        strategy: "email_code",
      });

      if (signInAttempt.status === "complete") {
        await setActive({ session: signInAttempt.createdSessionId });
        router.replace("/(tabs)");
      } else {
        createHandleErrorDialog({
          title: t("auth.error_signing_in"),
          error: new Error(t("auth.error_signing_in")),
        });
      }
    } catch (err: any) {
      createHandleErrorDialog({
        title: t("auth.error_signing_in"),
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = () =>
    startGoogleTransition(async () => {
      await handleGoogleOAuth();
    });

  const showVerificationInput = () => {
    setPendingVerification(true);
  };

  if (!isLoaded) return <Loading />;

  return (
    <SafeAreaView
      style={{
        flex: 1,
        flexDirection: "column",
      }}
    >
      <LinearGradient
        colors={["#E9E8ED", "#ceccd7"]}
        locations={[0.5, 0.9]}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          flex: 1,
          zIndex: -1,
          flexDirection: "column",
        }}
      />
      <ScrollView
        style={{
          flexDirection: "column",
          flex: 1,
          paddingTop: globalStyles.size.pageTop,
          paddingHorizontal: globalStyles.gap.xs,
          gap: globalStyles.gap.xs,
        }}
        keyboardShouldPersistTaps="handled"
      >
        <AuthHeader description={t("auth.sign_in_description")} />
        <View
          style={{
            flexDirection: "column",
            gap: globalStyles.gap.xs,
            marginTop: globalStyles.gap.xs,
            marginBottom: 100,
          }}
        >
          {pendingVerification ? (
            <>
              <StyledOtpInput setCode={setCode} onSubmit={handleVerifyCode} />
              <Button
                isLoading={isLoading}
                text={t("common.verify")}
                style={{ width: "100%" }}
                onPress={() => handleVerifyCode(code)}
              />
              <Button
                text={t("common.back")}
                type="link"
                style={{ alignSelf: "center" }}
                onPress={() => setPendingVerification(false)}
              />
            </>
          ) : (
            <>
              <InputField
                required
                autoCapitalize="none"
                placeholder={t("common.email")}
                autoComplete="email"
                value={formFields.email}
                onChangeText={(txt) => handleChange("email", txt.trim())}
                className="bg-white"
              />
              <Button
                isLoading={isLoading}
                text={t("auth.sign_in")}
                style={{ width: "100%" }}
                onPress={handleEmailSignIn}
              />

              {/* Divider */}
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginVertical: 5,
                }}
              >
                <View
                  style={{
                    flex: 1,
                    height: 1,
                    backgroundColor: globalStyles.rgba({ opacity: 0.4 }).light
                      .secondary,
                  }}
                />
                <Text
                  style={{
                    marginHorizontal: globalStyles.gap["2xs"],
                    fontSize: globalStyles.size.md,
                    color: globalStyles.rgba().light.secondary,
                  }}
                >
                  {t("auth.or_continue_with")}
                </Text>
                <View
                  style={{
                    flex: 1,
                    height: 1,
                    backgroundColor: globalStyles.rgba({ opacity: 0.4 }).light
                      .secondary,
                  }}
                />
              </View>

              {/* Google Sign In Button */}
              <GoogleSignInButton
                onPress={handleGoogleSignIn}
                isLoading={isGooglePending}
                style={{ width: "100%" }}
              />

              {/* <Button
                text={t("common.back")}
                type="link"
                style={{ alignSelf: "center" }}
                onPress={() => router.back()}
              /> */}
              <InfoLink
                description={t("auth.already_have_the")}
                linkText={t("auth.confirmation_code")}
                onPress={showVerificationInput}
              />
              <InfoLink
                description={t("auth.dont_have_an_account_yet")}
                linkText={t("auth.sign_up")}
                onPress={() => router.push("/sign-up")}
              />
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
